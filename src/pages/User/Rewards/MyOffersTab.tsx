import React, { useState, useEffect } from "react";
import { FaChevronDown, FaFilter, FaClock, FaStar } from "react-icons/fa";
import { dataScientApi, creditCardApi } from "../../../services/api";

interface Offer {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  iconBg: string;
  tags?: {
    label: string;
    color: string;
  }[];
  cards: {
    name: string;
    type?: string;
  }[];
  expirationDays?: number;
  value?: string;
}

const MyOffersTab: React.FC = () => {
  const [sortOption, setSortOption] = useState("Expiring Soon");
  const [offers, setOffers] = useState<Offer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch offers from DS API
  useEffect(() => {
    const fetchOffers = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // First, get user's credit cards
        const cardsResponse = await creditCardApi.getCards(1, 50);

        if (cardsResponse.success && cardsResponse.cards) {
          const cards = cardsResponse.cards;
          const cardNames = cards
            .filter((card: any) => !card.sponsored)
            .map((card: any) => card.name);

          if (cardNames.length > 0) {
            try {
              const offersResponse = await dataScientApi.getOffers(cardNames);

              if (offersResponse.success || Array.isArray(offersResponse)) {
                // Transform API response to UI format
                const transformedOffers = transformOffersResponse(offersResponse);
                setOffers(transformedOffers);
              } else {
                // Fall back to default offers
                setOffers(getDefaultOffers());
              }
            } catch (apiError) {
              console.warn("DS offers API failed, using defaults:", apiError);
              setOffers(getDefaultOffers());
            }
          } else {
            // No cards, use default offers
            setOffers(getDefaultOffers());
          }
        } else {
          // No cards, use default offers
          setOffers(getDefaultOffers());
        }
      } catch (err: any) {
        console.error("Failed to fetch offers:", err);
        setError(err.message || "Failed to load offers");
        setOffers(getDefaultOffers());
      } finally {
        setIsLoading(false);
      }
    };

    fetchOffers();
  }, []);

  // Transform API response to UI format
  const transformOffersResponse = (response: any): Offer[] => {
    // Handle the nested response format: { offers: [{ card_name, offers: [...] }] }
    const offersData = response.offers || [];
    const transformedOffers: Offer[] = [];

    offersData.forEach((cardOffers: any) => {
      const cardName = cardOffers.card_name;
      const cardUrl = cardOffers.card_url;

      // Map ALL offers for this card
      (cardOffers.offers || []).forEach((offer: any, index: number) => {
        transformedOffers.push({
          id: `${cardName.replace(/\s+/g, '_')}_offer_${index}`,
          title: offer.type || "Special Offer",
          description: offer.short_description || "Exclusive offer for your credit card",
          // Render SVG icon directly
          icon: offer.icon_svg ? (
            <div
              className="w-5 h-5 text-current"
              dangerouslySetInnerHTML={{ __html: offer.icon_svg }}
            />
          ) : getOfferIcon(offer.type),
          iconBg: getOfferIconBg(offer.type),
          tags: offer.highlight ? [{
            label: offer.highlight,
            color: getTagColor(offer.highlight)
          }] : undefined,
          cards: [{ name: cardName }],
          expirationDays: undefined, // Not provided in this API format
          value: offer.highlight || undefined
        });
      });
    });

    return transformedOffers;
  };

  // Get icon for offer type
  const getOfferIcon = (type: string) => {
    const lowerType = (type || '').toLowerCase();
    if (lowerType.includes('apr') || lowerType.includes('balance')) return <span className="text-blue-600 text-xl">%</span>;
    if (lowerType.includes('bonus') || lowerType.includes('signup')) return <span className="text-purple-600 text-xl">💳</span>;
    if (lowerType.includes('travel') || lowerType.includes('insurance')) return <span className="text-green-600 text-xl">✈️</span>;
    if (lowerType.includes('cashback') || lowerType.includes('rewards')) return <span className="text-blue-600 text-xl">💰</span>;
    return <span className="text-gray-600 text-xl">🎁</span>;
  };

  // Get icon background for offer type
  const getOfferIconBg = (type: string) => {
    const lowerType = (type || '').toLowerCase();
    if (lowerType.includes('apr') || lowerType.includes('balance')) return "bg-blue-100";
    if (lowerType.includes('bonus') || lowerType.includes('signup')) return "bg-purple-100";
    if (lowerType.includes('travel') || lowerType.includes('insurance')) return "bg-green-100";
    if (lowerType.includes('cashback') || lowerType.includes('rewards')) return "bg-blue-100";
    return "bg-gray-100";
  };

  // Get tag color
  const getTagColor = (tag: string) => {
    const lowerTag = tag.toLowerCase();
    if (lowerTag.includes('best') || lowerTag.includes('featured')) return "bg-purple-100 text-purple-700";
    if (lowerTag.includes('popular') || lowerTag.includes('trending')) return "bg-yellow-100 text-yellow-700";
    if (lowerTag.includes('available') || lowerTag.includes('active')) return "bg-green-100 text-green-700";
    if (lowerTag.includes('limited') || lowerTag.includes('exclusive')) return "bg-red-100 text-red-700";
    return "bg-gray-100 text-gray-700";
  };

  // Default offers fallback (based on API structure)
  const getDefaultOffers = (): Offer[] => [
    {
      id: "default_offer_1",
      title: "Travel Credit: Get $100 Annual Hotel Benefit",
      description: "Enjoy a $100 annual hotel benefit with the Citi Strata Premier Card",
      icon: <div className="w-5 h-5 text-current" dangerouslySetInnerHTML={{ __html: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-credit-card"><rect x="1" y="4" width="22" height="16" rx="2" ry="2" fill="none" stroke="none"/><path d="M1 10h22"/><path d="M1 14h22"/><path d="M1 18h22"/></svg>' }} />,
      iconBg: "bg-blue-100",
      tags: [{ label: "Worth $100", color: "bg-blue-100 text-blue-700" }],
      cards: [{ name: "Citi Strata Premier Card" }],
      value: "Worth $100",
    },
    {
      id: "default_offer_2",
      title: "Exclusive Access: Presale Tickets and Experiences",
      description: "Get special access to purchase presale tickets and exclusive experiences",
      icon: <div className="w-5 h-5 text-current" dangerouslySetInnerHTML={{ __html: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-ticket"><path d="M16 3h5l-5 14H4a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h5"/><path d="M10 15V6"/><path d="M6 15h4"/><path d="M10 9v6"/></svg>' }} />,
      iconBg: "bg-purple-100",
      tags: [{ label: "Thousands of events", color: "bg-purple-100 text-purple-700" }],
      cards: [{ name: "Citi Strata Premier Card" }],
      value: "Thousands of events",
    },
    {
      id: "default_offer_3",
      title: "Sign-up Bonus: Get 75,000 points with Amex Gold",
      description: "Get 75,000 points when you spend $4,000 in first 6 months",
      icon: <span className="text-yellow-600 text-xl">💳</span>,
      iconBg: "bg-yellow-100",
      tags: [{ label: "Worth $750", color: "bg-yellow-100 text-yellow-700" }],
      cards: [{ name: "Amex Gold" }],
      value: "Worth $750",
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="spinner animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <div className="flex items-center justify-center w-9 h-9 rounded-md bg-green-100">
            <span className="text-green-600 text-xl">%</span>
          </div>
          <h2 className="text-lg font-medium text-gray-800">My Offers</h2>
        </div>

        <div className="flex space-x-3">
          <div className="relative">
            <select
              className="appearance-none pl-3 pr-8 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={sortOption}
              onChange={(e) => setSortOption(e.target.value)}
            >
              <option>Expiring Soon</option>
              <option>Highest Value</option>
              <option>Recently Added</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <FaChevronDown className="h-3 w-3" />
            </div>
          </div>

          <button className="p-2 text-gray-500 hover:bg-gray-100 rounded-md">
            <FaFilter size={16} />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {offers.map((offer) => (
          <div
            key={offer.id}
            className="bg-white rounded-md border border-gray-200 p-5"
          >
            <div className="flex items-start mb-3">
              <div
                className={`w-10 h-10 rounded-md ${offer.iconBg} flex items-center justify-center mr-4`}
              >
                {offer.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-start justify-between">
                  <h3 className="font-medium text-gray-800">{offer.title}</h3>
                  {offer.expirationDays && (
                    <div className="flex items-center bg-red-50 text-red-600 text-xs px-2 py-1 rounded-md">
                      <FaClock className="mr-1" size={10} />
                      <span>{offer.expirationDays} days left</span>
                    </div>
                  )}
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {offer.description}
                </p>
              </div>
            </div>

            <div className="mt-3 flex flex-wrap gap-2">
              {offer.cards.map((card, idx) => (
                <div
                  key={idx}
                  className="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full"
                >
                  {card.name}
                  {card.type && (
                    <span className="ml-1 text-gray-500">{card.type}</span>
                  )}
                </div>
              ))}
            </div>

            <div className="mt-3 flex items-center justify-between">
              {offer.value && (
                <div className="text-sm font-medium text-gray-700">
                  {offer.value}
                </div>
              )}
              {offer.tags && (
                <div className="flex gap-2">
                  {offer.tags.map((tag, idx) => (
                    <div
                      key={idx}
                      className={`${tag.color} px-2 py-1 rounded-full text-xs font-medium flex items-center`}
                    >
                      {tag.label === "Best Offer" && (
                        <FaStar className="mr-1" size={10} />
                      )}
                      {tag.label}
                    </div>
                  ))}
                </div>
              )}
            </div>

            <button className="mt-4 text-sm text-blue-600 font-medium hover:text-blue-700">
              Learn More
            </button>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <button className="text-blue-600 border border-blue-200 rounded-md px-4 py-2 text-sm hover:bg-blue-50 flex items-center mx-auto">
          <span>View all offers</span>
          <FaChevronDown className="ml-2" size={12} />
        </button>
      </div>
    </div>
  );
};

export default MyOffersTab;
