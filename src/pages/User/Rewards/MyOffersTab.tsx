import React, { useState, useEffect } from "react";
import { FaChevronDown, FaFilter, FaClock, FaStar } from "react-icons/fa";
import { dataScientApi, creditCardApi } from "../../../services/api";

interface Offer {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  iconBg: string;
  tags?: {
    label: string;
    color: string;
  }[];
  cards: {
    name: string;
    type?: string;
  }[];
  expirationDays?: number;
  value?: string;
}

const MyOffersTab: React.FC = () => {
  const [sortOption, setSortOption] = useState("Expiring Soon");
  const [offers, setOffers] = useState<Offer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch offers from DS API
  useEffect(() => {
    const fetchOffers = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // First, get user's credit cards
        const cardsResponse = await creditCardApi.getCards(1, 50);

        if (cardsResponse.success && cardsResponse.cards) {
          const cards = cardsResponse.cards;
          const cardNames = cards
            .filter((card: any) => !card.sponsored)
            .map((card: any) => card.name);

          if (cardNames.length > 0) {
            try {
              const offersResponse = await dataScientApi.getOffers(cardNames);

              if (offersResponse.success || Array.isArray(offersResponse)) {
                // Transform API response to UI format
                const transformedOffers = transformOffersResponse(offersResponse);
                setOffers(transformedOffers);
              } else {
                // Fall back to default offers
                setOffers(getDefaultOffers());
              }
            } catch (apiError) {
              console.warn("DS offers API failed, using defaults:", apiError);
              setOffers(getDefaultOffers());
            }
          } else {
            // No cards, use default offers
            setOffers(getDefaultOffers());
          }
        } else {
          // No cards, use default offers
          setOffers(getDefaultOffers());
        }
      } catch (err: any) {
        console.error("Failed to fetch offers:", err);
        setError(err.message || "Failed to load offers");
        setOffers(getDefaultOffers());
      } finally {
        setIsLoading(false);
      }
    };

    fetchOffers();
  }, []);

  // Transform API response to UI format
  const transformOffersResponse = (response: any): Offer[] => {
    // Handle different response formats
    const offersData = Array.isArray(response) ? response : response.offers || [];

    return offersData.map((offer: any, index: number) => ({
      id: offer.id || `offer_${index}`,
      title: offer.title || offer.name || "Special Offer",
      description: offer.description || "Exclusive offer for your credit cards",
      icon: getOfferIcon(offer.type || offer.category),
      iconBg: getOfferIconBg(offer.type || offer.category),
      tags: offer.tags ? offer.tags.map((tag: any) => ({
        label: typeof tag === 'string' ? tag : tag.label,
        color: typeof tag === 'string' ? getTagColor(tag) : tag.color
      })) : undefined,
      cards: offer.applicable_cards ? offer.applicable_cards.map((card: any) => ({
        name: typeof card === 'string' ? card : card.name,
        type: typeof card === 'object' ? card.type : undefined
      })) : [],
      expirationDays: offer.expires_in_days || offer.expiration_days,
      value: offer.value || offer.savings || offer.reward_amount
    }));
  };

  // Get icon for offer type
  const getOfferIcon = (type: string) => {
    const lowerType = (type || '').toLowerCase();
    if (lowerType.includes('apr') || lowerType.includes('balance')) return <span className="text-blue-600 text-xl">%</span>;
    if (lowerType.includes('bonus') || lowerType.includes('signup')) return <span className="text-purple-600 text-xl">💳</span>;
    if (lowerType.includes('travel') || lowerType.includes('insurance')) return <span className="text-green-600 text-xl">✈️</span>;
    if (lowerType.includes('cashback') || lowerType.includes('rewards')) return <span className="text-blue-600 text-xl">💰</span>;
    return <span className="text-gray-600 text-xl">🎁</span>;
  };

  // Get icon background for offer type
  const getOfferIconBg = (type: string) => {
    const lowerType = (type || '').toLowerCase();
    if (lowerType.includes('apr') || lowerType.includes('balance')) return "bg-blue-100";
    if (lowerType.includes('bonus') || lowerType.includes('signup')) return "bg-purple-100";
    if (lowerType.includes('travel') || lowerType.includes('insurance')) return "bg-green-100";
    if (lowerType.includes('cashback') || lowerType.includes('rewards')) return "bg-blue-100";
    return "bg-gray-100";
  };

  // Get tag color
  const getTagColor = (tag: string) => {
    const lowerTag = tag.toLowerCase();
    if (lowerTag.includes('best') || lowerTag.includes('featured')) return "bg-purple-100 text-purple-700";
    if (lowerTag.includes('popular') || lowerTag.includes('trending')) return "bg-yellow-100 text-yellow-700";
    if (lowerTag.includes('available') || lowerTag.includes('active')) return "bg-green-100 text-green-700";
    if (lowerTag.includes('limited') || lowerTag.includes('exclusive')) return "bg-red-100 text-red-700";
    return "bg-gray-100 text-gray-700";
  };

  // Default offers fallback
  const getDefaultOffers = (): Offer[] => [
    {
      id: "1",
      title: "0% APR Balance Transfer for 12 months",
      description:
        "Transfer your balance from Chase Sapphire to Brex Business Card and save on interest payments.",
      icon: <span className="text-blue-600 text-xl">%</span>,
      iconBg: "bg-blue-100",
      cards: [{ name: "Chase Sapphire" }, { name: "Brex Business Card" }],
      expirationDays: 15,
      value: "Save up to $420",
    },
    {
      id: "2",
      title: "Sign-up Bonus: Get 50,000 points with Brex Business Card",
      description:
        "Apply today and earn 50,000 bonus points when you spend $5,000 in the first 3 months.",
      icon: <span className="text-purple-600 text-xl">💳</span>,
      iconBg: "bg-purple-100",
      tags: [{ label: "Best Offer", color: "bg-purple-100 text-purple-700" }],
      cards: [{ name: "Brex Business Card" }],
      value: "Worth $1,000+",
    },
    {
      id: "3",
      title: "Travel Insurance Perks with Chase Sapphire",
      description:
        "Get exclusive travel insurance for your summer vacation with every travel purchase.",
      icon: <span className="text-green-600 text-xl">✈️</span>,
      iconBg: "bg-green-100",
      tags: [{ label: "Available", color: "bg-green-100 text-green-700" }],
      cards: [{ name: "Chase Sapphire" }],
      value: "Limited Time",
    },
    {
      id: "4",
      title: "Capital One Venture Card - $500 Travel Rewards",
      description:
        "Apply for a Capital One Venture Card and earn $500 in travel rewards when you spend $3,000 in the first 3 months.",
      icon: <span className="text-blue-600 text-xl">✈️</span>,
      iconBg: "bg-blue-100",
      tags: [{ label: "Popular", color: "bg-yellow-100 text-yellow-700" }],
      cards: [{ name: "Capital One Venture" }],
      value: "2x Miles on All Purchases",
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="spinner animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <div className="flex items-center justify-center w-9 h-9 rounded-md bg-green-100">
            <span className="text-green-600 text-xl">%</span>
          </div>
          <h2 className="text-lg font-medium text-gray-800">My Offers</h2>
        </div>

        <div className="flex space-x-3">
          <div className="relative">
            <select
              className="appearance-none pl-3 pr-8 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={sortOption}
              onChange={(e) => setSortOption(e.target.value)}
            >
              <option>Expiring Soon</option>
              <option>Highest Value</option>
              <option>Recently Added</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <FaChevronDown className="h-3 w-3" />
            </div>
          </div>

          <button className="p-2 text-gray-500 hover:bg-gray-100 rounded-md">
            <FaFilter size={16} />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {offers.map((offer) => (
          <div
            key={offer.id}
            className="bg-white rounded-md border border-gray-200 p-5"
          >
            <div className="flex items-start mb-3">
              <div
                className={`w-10 h-10 rounded-md ${offer.iconBg} flex items-center justify-center mr-4`}
              >
                {offer.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-start justify-between">
                  <h3 className="font-medium text-gray-800">{offer.title}</h3>
                  {offer.expirationDays && (
                    <div className="flex items-center bg-red-50 text-red-600 text-xs px-2 py-1 rounded-md">
                      <FaClock className="mr-1" size={10} />
                      <span>{offer.expirationDays} days left</span>
                    </div>
                  )}
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {offer.description}
                </p>
              </div>
            </div>

            <div className="mt-3 flex flex-wrap gap-2">
              {offer.cards.map((card, idx) => (
                <div
                  key={idx}
                  className="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full"
                >
                  {card.name}
                  {card.type && (
                    <span className="ml-1 text-gray-500">{card.type}</span>
                  )}
                </div>
              ))}
            </div>

            <div className="mt-3 flex items-center justify-between">
              {offer.value && (
                <div className="text-sm font-medium text-gray-700">
                  {offer.value}
                </div>
              )}
              {offer.tags && (
                <div className="flex gap-2">
                  {offer.tags.map((tag, idx) => (
                    <div
                      key={idx}
                      className={`${tag.color} px-2 py-1 rounded-full text-xs font-medium flex items-center`}
                    >
                      {tag.label === "Best Offer" && (
                        <FaStar className="mr-1" size={10} />
                      )}
                      {tag.label}
                    </div>
                  ))}
                </div>
              )}
            </div>

            <button className="mt-4 text-sm text-blue-600 font-medium hover:text-blue-700">
              Learn More
            </button>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <button className="text-blue-600 border border-blue-200 rounded-md px-4 py-2 text-sm hover:bg-blue-50 flex items-center mx-auto">
          <span>View all offers</span>
          <FaChevronDown className="ml-2" size={12} />
        </button>
      </div>
    </div>
  );
};

export default MyOffersTab;
