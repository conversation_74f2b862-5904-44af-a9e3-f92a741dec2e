import React, { useState, useEffect } from "react";
import {
  FaTrophy,
  FaClock,
  FaChartLine,
  FaArrowUp,
  FaCreditCard,
  FaBriefcase,
  FaExclamationTriangle,
  FaChartPie,
} from "react-icons/fa";
import { dataScientApi } from "../../../services/api";

const OverviewTab: React.FC = () => {
  const [overviewData, setOverviewData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch overview data
  useEffect(() => {
    const fetchOverviewData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await dataScientApi.getOverview();

        if (response.success || response.total_rewards !== undefined) {
          setOverviewData(response);
        } else {
          // Use fallback data if API fails
          setOverviewData(getFallbackData());
        }
      } catch (err: any) {
        console.error("Failed to fetch overview data:", err);
        setError(err.message || "Failed to load overview data");
        // Use fallback data on error
        setOverviewData(getFallbackData());
      } finally {
        setIsLoading(false);
      }
    };

    fetchOverviewData();
  }, []);

  // Fallback data structure
  const getFallbackData = () => ({
    total_rewards: 5200,
    total_rewards_type: "Points",
    monthly_growth: 20,
    next_goal: "$50 Gift Card",
    goal_progress: 60,
    points_needed: 500,
    expiring_rewards: [
      {
        card_name: "Chase Sapphire",
        points: 1200,
        days_left: 30,
        card_icon: "CS"
      },
      {
        card_name: "Amex Gold",
        points: 800,
        days_left: 45,
        card_icon: "AG"
      }
    ],
    monthly_trend: [
      { month: "JAN", value: 400 },
      { month: "FEB", value: 600 },
      { month: "MAR", value: 800 },
      { month: "APR", value: 850 },
      { month: "MAY", value: 900 },
      { month: "JUN", value: 1200 },
    ]
  });

  // Use API data or fallback data
  const data = overviewData || getFallbackData();
  const monthlyData = data.monthly_trend || data.monthly_data || getFallbackData().monthly_trend;

  // Calculate the max value for scaling the chart
  const maxValue = Math.max(...monthlyData.map((d: any) => d.value));

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="spinner animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        {/* Total Rewards */}
        <div className="bg-white rounded-md border border-gray-200 p-4 sm:p-5 relative">
          <div className="text-gray-500 text-sm mb-1">Total Rewards</div>
          <div className="text-gray-500 text-xs mb-4">Across all cards</div>

          <div className="absolute top-5 right-5 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <FaTrophy className="text-blue-500" />
          </div>

          <div className="text-3xl font-medium mb-1">
            {data.total_rewards?.toLocaleString() || "5,200"}{" "}
            <span className="text-sm font-normal text-gray-500">
              {data.total_rewards_type || "Points"}
            </span>
          </div>

          <div className="flex items-center text-green-600 text-sm">
            <FaArrowUp className="mr-1" size={12} />
            {data.monthly_growth || 20} pts this month
          </div>

          <div className="mt-4 text-sm text-gray-600">
            Next Goal: {data.next_goal || "$50 Gift Card"}
            <div className="mt-1 bg-gray-200 rounded-full h-1.5">
              <div
                className="bg-[#16c66c] h-1.5 rounded-full"
                style={{ width: `${data.goal_progress || 60}%` }}
              ></div>
            </div>
            <div className="mt-1 text-right text-xs text-gray-500">
              {data.points_needed || 500} points left
            </div>
          </div>
        </div>

        {/* Expiring Rewards */}
        <div className="bg-white rounded-md border border-gray-200 p-4 sm:p-5 relative">
          <div className="text-gray-500 text-sm mb-1">Expiring Rewards</div>
          <div className="text-gray-500 text-xs mb-4">In the next 60 days</div>

          <div className="absolute top-5 right-5 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <FaClock className="text-red-500" />
          </div>

          <div className="space-y-4">
            {(data.expiring_rewards || getFallbackData().expiring_rewards).slice(0, 2).map((reward: any, index: number) => (
              <div key={index} className="flex items-start">
                <div className={`w-8 h-8 rounded-md ${
                  reward.card_icon === 'CS' ? 'bg-blue-500' :
                  reward.card_icon === 'AG' ? 'bg-purple-500' :
                  'bg-gray-500'
                } text-white flex items-center justify-center mr-3 mt-1`}>
                  <span className="text-xs">{reward.card_icon || reward.card_name?.substring(0, 2).toUpperCase()}</span>
                </div>
                <div>
                  <div className="text-sm font-medium">{reward.card_name}</div>
                  <div className="text-xs text-gray-500">{reward.points?.toLocaleString()} points</div>
                  <div className={`mt-1 text-xs ${
                    reward.days_left <= 30 ? 'text-red-500' :
                    reward.days_left <= 45 ? 'text-orange-500' :
                    'text-yellow-500'
                  }`}>
                    {reward.days_left} days left
                  </div>
                </div>
              </div>
            ))}
          </div>

          <button className="mt-4 w-full py-2 text-center text-blue-600 border border-blue-200 rounded-md text-sm hover:bg-blue-50">
            View All Expiring Rewards
          </button>
        </div>

        {/* Rewards Trend */}
        <div className="bg-white rounded-md border border-gray-200 p-4 sm:p-5 relative">
          <div className="text-gray-500 text-sm mb-1">Rewards Trend</div>
          <div className="text-gray-500 text-xs mb-4">
            Monthly rewards earned
          </div>

          <div className="absolute top-5 right-5 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
            <FaChartLine className="text-green-500" />
          </div>

          <div className="h-40 flex items-end space-x-2 sm:space-x-4 mt-4">
            {monthlyData.map((data, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className={`w-full bg-blue-${index === monthlyData.length - 1 ? "600" : "200"} rounded-t-sm`}
                  style={{ height: `${(data.value / maxValue) * 100}%` }}
                ></div>
                <div className="mt-2 text-xs text-gray-500">{data.month}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverviewTab;
