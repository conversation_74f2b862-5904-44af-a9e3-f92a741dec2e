import React, { useState, useEffect } from "react";
import { FaChevronDown, FaStar, FaSearch } from "react-icons/fa";
import { dataScientApi, creditCardApi } from "../../../services/api";

interface Perk {
  id: string;
  title: string;
  description: string;
  icon: string | React.ReactNode;
  backgroundColor: string;
  card: string;
  cardColor: string;
  tags: {
    name: string;
    color: string;
  }[];
}

const PerksAndBenefitsTab: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBenefit, setSelectedBenefit] = useState("All Benefits");
  const [perks, setPerks] = useState<Perk[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch benefits from DS API
  useEffect(() => {
    const fetchBenefits = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // First, get user's credit cards
        const cardsResponse = await creditCardApi.getCards(1, 50);

        if (cardsResponse.success && cardsResponse.cards) {
          const cards = cardsResponse.cards;
          const cardNames = cards
            .filter((card: any) => !card.sponsored)
            .map((card: any) => card.name);

          if (cardNames.length > 0) {
            try {
              const benefitsResponse = await dataScientApi.getBenefits(cardNames);

              if (benefitsResponse.success || Array.isArray(benefitsResponse)) {
                // Transform API response to UI format
                const transformedBenefits = transformBenefitsResponse(benefitsResponse);
                setPerks(transformedBenefits);
              } else {
                // Fall back to default benefits
                setPerks(getDefaultPerks());
              }
            } catch (apiError) {
              console.warn("DS benefits API failed, using defaults:", apiError);
              setPerks(getDefaultPerks());
            }
          } else {
            // No cards, use default benefits
            setPerks(getDefaultPerks());
          }
        } else {
          // No cards, use default benefits
          setPerks(getDefaultPerks());
        }
      } catch (err: any) {
        console.error("Failed to fetch benefits:", err);
        setError(err.message || "Failed to load benefits");
        setPerks(getDefaultPerks());
      } finally {
        setIsLoading(false);
      }
    };

    fetchBenefits();
  }, []);

  // Transform API response to UI format
  const transformBenefitsResponse = (response: any): Perk[] => {
    // Handle the nested response format: { benefits: [{ card_name, benefits: [...] }] }
    const benefitsData = response.benefits || [];
    const transformedBenefits: Perk[] = [];

    benefitsData.forEach((cardBenefits: any) => {
      const cardName = cardBenefits.card_name;
      const cardUrl = cardBenefits.card_url;

      // Map ALL benefits for this card
      (cardBenefits.benefits || []).forEach((benefit: any, index: number) => {
        transformedBenefits.push({
          id: `${cardName.replace(/\s+/g, '_')}_benefit_${index}`,
          title: benefit.title || "Card Benefit",
          description: benefit.description || "Exclusive benefit for your credit card",
          // Use SVG icon directly or fallback to emoji
          icon: benefit.svg ? benefit.svg : getBenefitIcon(benefit.category),
          backgroundColor: getBenefitBg(benefit.category),
          card: cardName,
          cardColor: getCardColor(cardName),
          tags: getBenefitDefaultTags(benefit.category)
        });
      });
    });

    return transformedBenefits;
  };

  // Get icon for benefit category
  const getBenefitIcon = (category: string) => {
    const lowerCategory = (category || '').toLowerCase();
    if (lowerCategory.includes('travel')) return "✈️";
    if (lowerCategory.includes('dining') || lowerCategory.includes('restaurant')) return "🍽️";
    if (lowerCategory.includes('shopping') || lowerCategory.includes('purchase')) return "🛍️";
    if (lowerCategory.includes('insurance') || lowerCategory.includes('protection')) return "🛡️";
    if (lowerCategory.includes('lounge') || lowerCategory.includes('airport')) return "🏢";
    if (lowerCategory.includes('concierge') || lowerCategory.includes('service')) return "🎩";
    return "💎";
  };

  // Get background color for benefit category
  const getBenefitBg = (category: string) => {
    const lowerCategory = (category || '').toLowerCase();
    if (lowerCategory.includes('travel')) return "bg-blue-500";
    if (lowerCategory.includes('dining')) return "bg-yellow-500";
    if (lowerCategory.includes('shopping')) return "bg-purple-500";
    if (lowerCategory.includes('insurance')) return "bg-green-500";
    if (lowerCategory.includes('lounge')) return "bg-indigo-500";
    if (lowerCategory.includes('concierge')) return "bg-gray-500";
    return "bg-amber-500";
  };

  // Get card color
  const getCardColor = (cardName: string) => {
    const lowerName = (cardName || '').toLowerCase();
    if (lowerName.includes('chase') || lowerName.includes('sapphire')) return "text-blue-600";
    if (lowerName.includes('amex') || lowerName.includes('american express')) return "text-purple-600";
    if (lowerName.includes('citi')) return "text-red-600";
    if (lowerName.includes('capital one')) return "text-orange-600";
    if (lowerName.includes('discover')) return "text-orange-500";
    return "text-gray-600";
  };

  // Get tag color for benefit
  const getBenefitTagColor = (tag: string) => {
    const lowerTag = tag.toLowerCase();
    if (lowerTag.includes('premium') || lowerTag.includes('exclusive')) return "bg-purple-100 text-purple-700";
    if (lowerTag.includes('popular') || lowerTag.includes('featured')) return "bg-yellow-100 text-yellow-700";
    if (lowerTag.includes('travel') || lowerTag.includes('global')) return "bg-blue-100 text-blue-700";
    if (lowerTag.includes('dining') || lowerTag.includes('restaurant')) return "bg-orange-100 text-orange-700";
    return "bg-gray-100 text-gray-700";
  };

  // Get default tags for benefit category
  const getBenefitDefaultTags = (category: string) => {
    const lowerCategory = (category || '').toLowerCase();
    if (lowerCategory.includes('travel')) return [{ name: "Travel", color: "bg-blue-100 text-blue-700" }];
    if (lowerCategory.includes('dining')) return [{ name: "Dining", color: "bg-orange-100 text-orange-700" }];
    if (lowerCategory.includes('shopping')) return [{ name: "Shopping", color: "bg-purple-100 text-purple-700" }];
    if (lowerCategory.includes('insurance')) return [{ name: "Protection", color: "bg-green-100 text-green-700" }];
    return [{ name: "Premium", color: "bg-gray-100 text-gray-700" }];
  };

  // Default perks fallback (based on API structure)
  const getDefaultPerks = (): Perk[] => [
    {
      id: "default_benefit_1",
      title: "Travel Insurance",
      description: "Stay protected with travel insurances, including trip delay protection, common carrier trip cancellation and interruption protection, lost or damaged luggage, and more.",
      icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shield"><path d="M12 22s-1-5.5-6-5.5S4 12 4 12s1 5.5 6 5.5S20 12 20 12z"/><path d="M12 8v12"/><path d="M12 19l-6-6 6-6"/></svg>',
      backgroundColor: "bg-green-500",
      card: "Citi Strata Premier Card",
      cardColor: "text-red-600",
      tags: [{ name: "Insurance", color: "bg-green-100 text-green-800" }],
    },
    {
      id: "default_benefit_2",
      title: "Trip Cancellation and Interruption",
      description: "Get protected against trip cancellations and interruptions with common carrier coverage.",
      icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-calendar"><path d="M20 3v2h-6l2 7h-6"/><path d="M16 19h-6"/><path d="M2 3v2h20V3z"/><path d="M16 9h6"/></svg>',
      backgroundColor: "bg-blue-500",
      card: "Citi Strata Premier Card",
      cardColor: "text-red-600",
      tags: [{ name: "Insurance", color: "bg-green-100 text-green-800" }],
    },
    {
      id: "default_benefit_3",
      title: "Travel Credits",
      description: "Earn rewards with travel purchases and redeem for travel credits.",
      icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-credit-card"><rect x="1" y="4" width="22" height="16" rx="2" ry="2" fill="none" stroke="currentColor"/><path d="M1 10h6m10 0h6"/></svg>',
      backgroundColor: "bg-purple-500",
      card: "Citi Strata Premier Card",
      cardColor: "text-red-600",
      tags: [{ name: "Travel", color: "bg-blue-100 text-blue-800" }],
    },
    {
      id: "default_benefit_4",
      title: "Presale Tickets and Exclusive Events",
      description: "Get special access to purchase tickets to thousands of events, including presale tickets and exclusive experiences.",
      icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-ticket"><path d="M2 3v2h20V3z"/><path d="M16 7H5a2 2 0 0 0-2 2v11a2 2 0 0 0 2 2h11a2 2 0 0 0 2-2v-9h-6z"/><path d="M15 6v12"/></svg>',
      backgroundColor: "bg-indigo-500",
      card: "Citi Strata Premier Card",
      cardColor: "text-red-600",
      tags: [{ name: "Events", color: "bg-purple-100 text-purple-800" }],
    },
  ];

  // Filter perks based on search and benefit type
  const filteredPerks = perks.filter(perk => {
    const matchesSearch = perk.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         perk.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         perk.card.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesBenefit = selectedBenefit === "All Benefits" ||
                          perk.tags.some(tag => tag.name.toLowerCase().includes(selectedBenefit.toLowerCase()));

    return matchesSearch && matchesBenefit;
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="spinner animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <div className="flex items-center justify-center w-9 h-9 rounded-md bg-amber-100">
            <span className="text-amber-600 text-xl">💎</span>
          </div>
          <h2 className="text-lg font-medium text-gray-800">
            Perks & Benefits
          </h2>
        </div>

        <div className="flex space-x-3">
          <button className="bg-yellow-100 text-yellow-600 px-4 py-2 rounded-md text-sm flex items-center">
            <FaStar className="mr-2" />
            Favorites
          </button>

          <div className="relative w-56">
            <input
              type="text"
              placeholder="Search card"
              className="w-full pl-3 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="relative">
            <select
              className="appearance-none pl-3 pr-8 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={selectedBenefit}
              onChange={(e) => setSelectedBenefit(e.target.value)}
            >
              <option>All Benefits</option>
              <option>Travel</option>
              <option>Dining</option>
              <option>Shopping</option>
              <option>Insurance</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <FaChevronDown className="h-3 w-3" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {filteredPerks.map((perk) => (
          <div
            key={perk.id}
            className="bg-white rounded-md border border-gray-200 overflow-hidden relative"
          >
            <div
              className={`h-36 ${perk.backgroundColor} relative flex items-center justify-center`}
            >
              {typeof perk.icon === 'string' && perk.icon.includes('<svg') ? (
                <div
                  className="w-16 h-16 text-white"
                  dangerouslySetInnerHTML={{ __html: perk.icon }}
                />
              ) : (
                <span className="text-white text-5xl">{perk.icon}</span>
              )}
              <button className="absolute top-2 left-2 text-white rounded-full p-1.5 hover:bg-white hover:bg-opacity-20">
                <FaStar className="h-5 w-5" />
              </button>
              <div className="absolute top-2 right-2 px-3 py-1 rounded-md text-sm text-white bg-black bg-opacity-20">
                {perk.card}
              </div>
            </div>
            <div className="p-4">
              <h3 className="font-medium text-gray-800 text-lg mb-2">
                {perk.title}
              </h3>
              <p className="text-sm text-gray-600 mb-4">{perk.description}</p>
              <div className="flex flex-wrap gap-2">
                {perk.tags.map((tag, idx) => (
                  <div
                    key={idx}
                    className={`${tag.color} px-3 py-1 rounded-full text-xs`}
                  >
                    {tag.name}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <button className="text-blue-600 border border-blue-200 rounded-md px-4 py-2 text-sm hover:bg-blue-50 flex items-center mx-auto">
          <span>View all perks & benefits</span>
          <FaChevronDown className="ml-2" size={12} />
        </button>
      </div>
    </div>
  );
};

export default PerksAndBenefitsTab;
