import React, { useState, useEffect } from "react";
import { FaChevronDown, FaStar, FaSearch } from "react-icons/fa";
import { dataScientApi, creditCardApi } from "../../../services/api";

interface Perk {
  id: string;
  title: string;
  description: string;
  icon: string;
  backgroundColor: string;
  card: string;
  cardColor: string;
  tags: {
    name: string;
    color: string;
  }[];
}

const PerksAndBenefitsTab: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBenefit, setSelectedBenefit] = useState("All Benefits");
  const [perks, setPerks] = useState<Perk[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch benefits from DS API
  useEffect(() => {
    const fetchBenefits = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // First, get user's credit cards
        const cardsResponse = await creditCardApi.getCards(1, 50);

        if (cardsResponse.success && cardsResponse.cards) {
          const cards = cardsResponse.cards;
          const cardNames = cards
            .filter((card: any) => !card.sponsored)
            .map((card: any) => card.name);

          if (cardNames.length > 0) {
            try {
              const benefitsResponse = await dataScientApi.getBenefits(cardNames);

              if (benefitsResponse.success || Array.isArray(benefitsResponse)) {
                // Transform API response to UI format
                const transformedBenefits = transformBenefitsResponse(benefitsResponse);
                setPerks(transformedBenefits);
              } else {
                // Fall back to default benefits
                setPerks(getDefaultPerks());
              }
            } catch (apiError) {
              console.warn("DS benefits API failed, using defaults:", apiError);
              setPerks(getDefaultPerks());
            }
          } else {
            // No cards, use default benefits
            setPerks(getDefaultPerks());
          }
        } else {
          // No cards, use default benefits
          setPerks(getDefaultPerks());
        }
      } catch (err: any) {
        console.error("Failed to fetch benefits:", err);
        setError(err.message || "Failed to load benefits");
        setPerks(getDefaultPerks());
      } finally {
        setIsLoading(false);
      }
    };

    fetchBenefits();
  }, []);

  // Transform API response to UI format
  const transformBenefitsResponse = (response: any): Perk[] => {
    // Handle different response formats
    const benefitsData = Array.isArray(response) ? response : response.benefits || [];

    return benefitsData.map((benefit: any, index: number) => ({
      id: benefit.id || `benefit_${index}`,
      title: benefit.title || benefit.name || "Card Benefit",
      description: benefit.description || "Exclusive benefit for your credit card",
      icon: getBenefitIcon(benefit.category || benefit.type),
      backgroundColor: getBenefitBg(benefit.category || benefit.type),
      card: benefit.card_name || benefit.applicable_card || "Your Card",
      cardColor: getCardColor(benefit.card_name || benefit.applicable_card),
      tags: benefit.tags ? benefit.tags.map((tag: any) => ({
        name: typeof tag === 'string' ? tag : tag.name,
        color: typeof tag === 'string' ? getBenefitTagColor(tag) : tag.color
      })) : getBenefitDefaultTags(benefit.category || benefit.type)
    }));
  };

  // Get icon for benefit category
  const getBenefitIcon = (category: string) => {
    const lowerCategory = (category || '').toLowerCase();
    if (lowerCategory.includes('travel')) return "✈️";
    if (lowerCategory.includes('dining') || lowerCategory.includes('restaurant')) return "🍽️";
    if (lowerCategory.includes('shopping') || lowerCategory.includes('purchase')) return "🛍️";
    if (lowerCategory.includes('insurance') || lowerCategory.includes('protection')) return "🛡️";
    if (lowerCategory.includes('lounge') || lowerCategory.includes('airport')) return "🏢";
    if (lowerCategory.includes('concierge') || lowerCategory.includes('service')) return "🎩";
    return "💎";
  };

  // Get background color for benefit category
  const getBenefitBg = (category: string) => {
    const lowerCategory = (category || '').toLowerCase();
    if (lowerCategory.includes('travel')) return "bg-blue-500";
    if (lowerCategory.includes('dining')) return "bg-yellow-500";
    if (lowerCategory.includes('shopping')) return "bg-purple-500";
    if (lowerCategory.includes('insurance')) return "bg-green-500";
    if (lowerCategory.includes('lounge')) return "bg-indigo-500";
    if (lowerCategory.includes('concierge')) return "bg-gray-500";
    return "bg-amber-500";
  };

  // Get card color
  const getCardColor = (cardName: string) => {
    const lowerName = (cardName || '').toLowerCase();
    if (lowerName.includes('chase') || lowerName.includes('sapphire')) return "text-blue-600";
    if (lowerName.includes('amex') || lowerName.includes('american express')) return "text-purple-600";
    if (lowerName.includes('citi')) return "text-red-600";
    if (lowerName.includes('capital one')) return "text-orange-600";
    if (lowerName.includes('discover')) return "text-orange-500";
    return "text-gray-600";
  };

  // Get tag color for benefit
  const getBenefitTagColor = (tag: string) => {
    const lowerTag = tag.toLowerCase();
    if (lowerTag.includes('premium') || lowerTag.includes('exclusive')) return "bg-purple-100 text-purple-700";
    if (lowerTag.includes('popular') || lowerTag.includes('featured')) return "bg-yellow-100 text-yellow-700";
    if (lowerTag.includes('travel') || lowerTag.includes('global')) return "bg-blue-100 text-blue-700";
    if (lowerTag.includes('dining') || lowerTag.includes('restaurant')) return "bg-orange-100 text-orange-700";
    return "bg-gray-100 text-gray-700";
  };

  // Get default tags for benefit category
  const getBenefitDefaultTags = (category: string) => {
    const lowerCategory = (category || '').toLowerCase();
    if (lowerCategory.includes('travel')) return [{ name: "Travel", color: "bg-blue-100 text-blue-700" }];
    if (lowerCategory.includes('dining')) return [{ name: "Dining", color: "bg-orange-100 text-orange-700" }];
    if (lowerCategory.includes('shopping')) return [{ name: "Shopping", color: "bg-purple-100 text-purple-700" }];
    if (lowerCategory.includes('insurance')) return [{ name: "Protection", color: "bg-green-100 text-green-700" }];
    return [{ name: "Premium", color: "bg-gray-100 text-gray-700" }];
  };

  // Default perks fallback
  const getDefaultPerks = (): Perk[] => [
    {
      id: "1",
      title: "Airport Lounge Access",
      description:
        "Free access to over 1,000 airport lounges worldwide including Priority Pass and Centurion Lounges.",
      icon: "✈️",
      backgroundColor: "bg-blue-500",
      card: "Amex Platinum",
      cardColor: "text-blue-600",
      tags: [{ name: "Travel", color: "bg-blue-100 text-blue-800" }],
    },
    {
      id: "2",
      title: "Travel Insurance",
      description:
        "Comprehensive travel insurance including trip cancellation, baggage delay, and emergency assistance.",
      icon: "🛡️",
      backgroundColor: "bg-green-500",
      card: "Chase Sapphire",
      cardColor: "text-blue-600",
      tags: [{ name: "Insurance", color: "bg-green-100 text-green-800" }],
    },
    {
      id: "3",
      title: "Dining Credits",
      description:
        "$120 annual dining credit at select restaurants and food delivery services, applied as $10 monthly credits.",
      icon: "🍴",
      backgroundColor: "bg-purple-500",
      card: "Amex Gold",
      cardColor: "text-purple-600",
      tags: [{ name: "Dining", color: "bg-purple-100 text-purple-800" }],
    },
    {
      id: "4",
      title: "Extended Warranty",
      description:
        "Extends the manufacturer's warranty by up to 2 years on eligible purchases made with your card.",
      icon: "🕒",
      backgroundColor: "bg-amber-500",
      card: "Citi Double Cash",
      cardColor: "text-red-600",
      tags: [{ name: "Shopping", color: "bg-yellow-100 text-yellow-800" }],
    },
    {
      id: "5",
      title: "Price Protection",
      description:
        "If you find a lower price on an item within 90 days of purchase, you can be reimbursed the difference.",
      icon: "🏷️",
      backgroundColor: "bg-red-500",
      card: "Discover It",
      cardColor: "text-orange-500",
      tags: [{ name: "Shopping", color: "bg-red-100 text-red-800" }],
    },
    {
      id: "6",
      title: "Global Entry/TSA PreCheck",
      description:
        "Statement credit of up to $100 every 4 years for Global Entry or TSA PreCheck application fee.",
      icon: "🌐",
      backgroundColor: "bg-blue-500",
      card: "Chase Sapphire",
      cardColor: "text-blue-600",
      tags: [{ name: "Travel", color: "bg-blue-100 text-blue-800" }],
    },
  ];

  // Filter perks based on search and benefit type
  const filteredPerks = perks.filter(perk => {
    const matchesSearch = perk.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         perk.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         perk.card.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesBenefit = selectedBenefit === "All Benefits" ||
                          perk.tags.some(tag => tag.name.toLowerCase().includes(selectedBenefit.toLowerCase()));

    return matchesSearch && matchesBenefit;
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="spinner animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <div className="flex items-center justify-center w-9 h-9 rounded-md bg-amber-100">
            <span className="text-amber-600 text-xl">💎</span>
          </div>
          <h2 className="text-lg font-medium text-gray-800">
            Perks & Benefits
          </h2>
        </div>

        <div className="flex space-x-3">
          <button className="bg-yellow-100 text-yellow-600 px-4 py-2 rounded-md text-sm flex items-center">
            <FaStar className="mr-2" />
            Favorites
          </button>

          <div className="relative w-56">
            <input
              type="text"
              placeholder="Search card"
              className="w-full pl-3 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="relative">
            <select
              className="appearance-none pl-3 pr-8 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={selectedBenefit}
              onChange={(e) => setSelectedBenefit(e.target.value)}
            >
              <option>All Benefits</option>
              <option>Travel</option>
              <option>Dining</option>
              <option>Shopping</option>
              <option>Insurance</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <FaChevronDown className="h-3 w-3" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {filteredPerks.map((perk) => (
          <div
            key={perk.id}
            className="bg-white rounded-md border border-gray-200 overflow-hidden relative"
          >
            <div
              className={`h-36 ${perk.backgroundColor} relative flex items-center justify-center`}
            >
              <span className="text-white text-5xl">{perk.icon}</span>
              <button className="absolute top-2 left-2 text-white rounded-full p-1.5 hover:bg-white hover:bg-opacity-20">
                <FaStar className="h-5 w-5" />
              </button>
              <div className="absolute top-2 right-2 px-3 py-1 rounded-md text-sm text-white bg-black bg-opacity-20">
                {perk.card}
              </div>
            </div>
            <div className="p-4">
              <h3 className="font-medium text-gray-800 text-lg mb-2">
                {perk.title}
              </h3>
              <p className="text-sm text-gray-600 mb-4">{perk.description}</p>
              <div className="flex flex-wrap gap-2">
                {perk.tags.map((tag, idx) => (
                  <div
                    key={idx}
                    className={`${tag.color} px-3 py-1 rounded-full text-xs`}
                  >
                    {tag.name}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <button className="text-blue-600 border border-blue-200 rounded-md px-4 py-2 text-sm hover:bg-blue-50 flex items-center mx-auto">
          <span>View all perks & benefits</span>
          <FaChevronDown className="ml-2" size={12} />
        </button>
      </div>
    </div>
  );
};

export default PerksAndBenefitsTab;
