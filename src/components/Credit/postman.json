{"info": {"_postman_id": "7caeb754-fc9f-4f0a-ae82-8d611fe27b59", "name": "StackEasy", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24016493"}, "item": [{"name": "Best Card by Category", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/cards/best-by-category/:category?limit=10", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "cards", "best-by-category", ":category"], "query": [{"key": "limit", "value": "10"}], "variable": [{"key": "category", "value": ""}]}}, "response": []}, {"name": "Spending Insight", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"spending_patterns\": {\n    \"dining\": 0,\n    \"travel\": 0,\n    \"groceries\": 0,\n    \"gas\": 0,\n    \"entertainment\": 0,\n    \"shopping\": 0,\n    \"business\": 0\n  },\n  \"credit_score_range\": \"good\",\n  \"preferences\": [],\n  \"current_cards\": [],\n  \"monthly_income\": 0\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/analysis/spending-insights", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "analysis", "spending-insights"]}}, "response": []}, {"name": "Personalized Recommendation", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"spending_patterns\": {\n    \"dining\": 0,\n    \"travel\": 0,\n    \"groceries\": 0,\n    \"gas\": 0,\n    \"entertainment\": 0,\n    \"shopping\": 0,\n    \"business\": 0\n  },\n  \"credit_score_range\": \"good\",\n  \"preferences\": [],\n  \"current_cards\": [],\n  \"monthly_income\": 0\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/recommendations/personalized", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "recommendations", "personalized"]}}, "response": []}, {"name": "Search Card", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/cards/search?query=1&card_type=1&max_annual_fee=1&limit=10", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "cards", "search"], "query": [{"key": "query", "value": "1"}, {"key": "card_type", "value": "1"}, {"key": "max_annual_fee", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Overview", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/stats/overview", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "stats", "overview"]}}, "response": []}, {"name": "Compare Cards", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/cards/compare?card_names=[test]", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "cards", "compare"], "query": [{"key": "card_names", "value": "[test]"}]}}, "response": []}, {"name": "Card Rewards", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/rewards/by-card/:cardName", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "rewards", "by-card", ":cardName"], "variable": [{"key": "cardName", "value": ""}]}}, "response": []}, {"name": "Card Benefit", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/benefits/by-card/:cardName", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "benefits", "by-card", ":cardName"], "variable": [{"key": "cardName", "value": ""}]}}, "response": []}, {"name": "Compare Category", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/rewards/compare-category/:category", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "rewards", "compare-category", ":category"], "variable": [{"key": "category", "value": ""}]}}, "response": []}, {"name": "Benefit Search", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/benefits/search?query=test&benefit_type=test&limit=test", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "benefits", "search"], "query": [{"key": "query", "value": "test"}, {"key": "benefit_type", "value": "test"}, {"key": "limit", "value": "test"}]}}, "response": []}, {"name": "Benefit Category", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/benefits/categories", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "benefits", "categories"]}}, "response": []}, {"name": "AI Recommendation", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"card_names\": [\n    \"string\"\n  ],\n  \"user_context\": {\n    \"spending_patterns\": {\n      \"additionalProp1\": 0,\n      \"additionalProp2\": 0,\n      \"additionalProp3\": 0\n    },\n    \"credit_score\": \"string\",\n    \"current_cards\": [\n      \"string\"\n    ],\n    \"preferences\": [\n      \"string\"\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:3048/v3/api/ds/ai/recommendations", "host": ["localhost"], "port": "3048", "path": ["v3", "api", "ds", "ai", "recommendations"]}}, "response": []}]}