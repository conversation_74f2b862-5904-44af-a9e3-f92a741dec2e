import axios from "axios";

// Get API URL from environment or use default
const API_URL = "https://mkdlabs.com/v3/api";

// Create an axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor to attach auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("auth_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Authentication API service
export const authApi = {
  // Register a new user
  register: async (userData: {
    fullName: string;
    email: string;
    password: string;
    confirmPassword: string;
  }) => {
    const response = await api.post("/auth/register", userData);
    return response.data;
  },

  // Login user
  login: async (credentials: {
    email: string;
    password: string;
    rememberMe?: boolean;
  }) => {
    const response = await api.post("/auth/login", credentials);
    if (response.data.success && response.data.token) {
      localStorage.setItem("auth_token", response.data.token);
      localStorage.setItem("user", JSON.stringify(response.data.user));
    }
    return response.data;
  },

  // Logout user
  logout: () => {
    localStorage.removeItem("auth_token");
    localStorage.removeItem("user");
    return { success: true };
  },

  // Check if user is logged in
  isLoggedIn: () => {
    return !!localStorage.getItem("auth_token");
  },

  // Get current user
  getCurrentUser: () => {
    const userJson = localStorage.getItem("user");
    return userJson ? JSON.parse(userJson) : null;
  },

  // Forgot password
  forgotPassword: async (email: string) => {
    const response = await api.post("/auth/forgot-password", { email });
    return response.data;
  },

  // Reset password
  resetPassword: async (data: {
    token: string;
    password: string;
    confirmPassword: string;
  }) => {
    const response = await api.post("/auth/reset-password", data);
    return response.data;
  },

  // Verify reset token
  verifyResetToken: async (token: string) => {
    const response = await api.get(`/auth/verify-reset-token/${token}`);
    return response.data;
  },
};

// User API service
export const userApi = {
  // Get current user profile
  getProfile: async () => {
    const response = await api.get("/users/profile");
    return response.data;
  },

  // Update user profile
  updateProfile: async (profileData: { fullName: string }) => {
    const response = await api.put("/users/profile", profileData);
    if (response.data.success && response.data.user) {
      localStorage.setItem("user", JSON.stringify(response.data.user));
    }
    return response.data;
  },

  // Change password
  changePassword: async (passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }) => {
    const response = await api.post("/users/change-password", passwordData);
    return response.data;
  },
};

// Credit Card API service
export const creditCardApi = {
  // Get all credit cards with pagination
  getCards: async (page = 1, limit = 10) => {
    const response = await api.get(`/credit-cards?page=${page}&limit=${limit}`);
    return response.data;
  },

  // Check if user has credit cards
  hasCards: async () => {
    const response = await api.get("/credit-cards/has-cards");
    return response.data;
  },

  // Get credit usage statistics
  getStats: async () => {
    const response = await api.get("/credit-cards/stats");
    return response.data;
  },

  // Get promotional cards
  getPromotionalCards: async () => {
    const response = await api.get("/credit-cards/promotional");
    return response.data;
  },

  // Get a single card by ID
  getCard: async (cardId: number | string) => {
    const response = await api.get(`/credit-cards/${cardId}`);
    return response.data;
  },

  // Add a new credit card
  addCard: async (cardData: {
    name: string;
    type?: string;
    issuer: string;
    cardNumber: string;
    icon?: string;
    currentBalance?: number;
    creditLimit?: number;
    paymentDueDate?: string;
    dueAmount?: number;
    aprEndDate?: string;
    status?: "good" | "confirm" | "expired";
    tags?: string[];
    balance_transfer?: number;
  }) => {
    const response = await api.post("/credit-cards", cardData);
    return response.data;
  },

  // Update a credit card
  updateCard: async (
    cardId: number | string,
    cardData: {
      name?: string;
      type?: string;
      currentBalance?: number;
      creditLimit?: number;
      paymentDueDate?: string;
      dueAmount?: number;
      aprEndDate?: string;
      status?: "good" | "confirm" | "expired";
      tags?: string[];
      balance_transfer?: number;
    }
  ) => {
    const response = await api.put(`/credit-cards/${cardId}`, cardData);
    return response.data;
  },

  // Delete a credit card
  deleteCard: async (cardId: number | string) => {
    const response = await api.delete(`/credit-cards/${cardId}`);
    return response.data;
  },
};

// AI Recommendations API service
export const aiRecommendationsApi = {
  // Get AI recommendations for the current user (legacy endpoint)
  getRecommendations: async () => {
    const response = await api.get(`/ai-recommendations`);
    return response.data;
  },

  // Get AI recommendations using the new DS endpoint
  getPersonalizedRecommendations: async (payload: {
    card_names: string[];
    user_context: {
      spending_patterns: {
        dining: number;
        travel: number;
        groceries: number;
        gas: number;
        entertainment: number;
        shopping: number;
      };
      preferences: string[];
      current_cards: string[];
    };
  }) => {
    const response = await api.post(`/ds/ai/recommendations`, payload);
    return response.data;
  },

  // Create a new AI recommendation
  createRecommendation: async (recommendationData: {
    type: string;
    title: string;
    description: string;
    icon?: string;
    iconBg?: string;
    relatedCards?: string[] | string;
    actionText?: string;
    actionVariant?: string;
    priority?: number;
  }) => {
    const response = await api.post(`/ai-recommendations`, recommendationData);
    return response.data;
  },

  // Update an AI recommendation
  updateRecommendation: async (
    recommendationId: number | string,
    recommendationData: {
      type?: string;
      title?: string;
      description?: string;
      icon?: string;
      iconBg?: string;
      relatedCards?: string[] | string;
      actionText?: string;
      actionVariant?: string;
      priority?: number;
      isActive?: boolean;
    }
  ) => {
    const response = await api.put(
      `/ai-recommendations/${recommendationId}`,
      recommendationData
    );
    return response.data;
  },

  // Delete an AI recommendation
  deleteRecommendation: async (recommendationId: number | string) => {
    const response = await api.delete(
      `/ai-recommendations/${recommendationId}`
    );
    return response.data;
  },
};

// Spending Insights API service
export const spendingInsightsApi = {
  // Get spending insights for the current user
  getInsights: async () => {
    const response = await api.get(`/spending-insights`);
    return response.data;
  },

  // Create a new spending insight
  createInsight: async (insightData: {
    category: string;
    title: string;
    description: string;
    amount?: number;
    percentage?: number;
    timePeriod?: string;
    periodStart?: string;
    periodEnd?: string;
    recommendedCardId?: number;
    icon?: string;
    iconBg?: string;
    actionText?: string;
    actionUrl?: string;
  }) => {
    const response = await api.post(`/spending-insights`, insightData);
    return response.data;
  },

  // Update a spending insight
  updateInsight: async (
    insightId: number | string,
    insightData: {
      category?: string;
      title?: string;
      description?: string;
      amount?: number;
      percentage?: number;
      timePeriod?: string;
      periodStart?: string;
      periodEnd?: string;
      recommendedCardId?: number;
      icon?: string;
      iconBg?: string;
      actionText?: string;
      actionUrl?: string;
      isActive?: boolean;
    }
  ) => {
    const response = await api.put(
      `/spending-insights/${insightId}`,
      insightData
    );
    return response.data;
  },

  // Delete a spending insight
  deleteInsight: async (insightId: number | string) => {
    const response = await api.delete(`/spending-insights/${insightId}`);
    return response.data;
  },
};

// Affiliate Offers API service
export interface AffiliateOffer {
  id: number;
  name: string;
  category: string;
  description: string;
  tags: string[];
  categories: string[];
  backgroundColor: string;
  icon: string;
  partnerUrl: string;
  featured: boolean;
  sponsored: boolean;
  priority?: number;
}

export const affiliateOffersApi = {
  // Get all affiliate offers
  getOffers: async (featured?: boolean): Promise<{
    success: boolean;
    offers: AffiliateOffer[];
    message?: string;
  }> => {
    const params = featured !== undefined ? { featured } : {};
    const response = await api.get("/affiliate-offers", { params });
    return response.data;
  },

  // Get featured affiliate offers
  getFeaturedOffers: async (): Promise<{
    success: boolean;
    offers: AffiliateOffer[];
    message?: string;
  }> => {
    const response = await api.get("/affiliate-offers/featured");
    return response.data;
  },

  // Get sponsored affiliate offers for home page
  getSponsoredOffers: async (limit = 3): Promise<{
    success: boolean;
    offers: AffiliateOffer[];
    message?: string;
  }> => {
    const response = await api.get("/affiliate-offers/sponsored", {
      params: { limit }
    });
    return response.data;
  },

  // Track affiliate click
  trackClick: async (offerId: number): Promise<{
    success: boolean;
    message?: string;
  }> => {
    const response = await api.post(`/affiliate-offers/${offerId}/track`);
    return response.data;
  },
};

export interface NotificationItem {
  id: string;
  type: string; // payment, reward, offer, security, etc.
  icon: string; // identifier for the icon type (can be mapped to React components in UI)
  title: string; // short title/summary
  message: string; // detailed message
  time: string; // timestamp
  unread: boolean;
  relatedEntityId?: string; // e.g., card ID or transaction ID
  relatedEntityType?: string; // e.g., "card", "transaction"
  actionUrl?: string; // URL for the "Take Action" button if applicable
  metadata?: Record<string, any>; // Additional data specific to the notification type
}

interface FetchNotificationsParams {
  page?: number;
  limit?: number;
  unreadOnly?: boolean;
  types?: string[];
  sortBy?: "date" | "type";
  sortOrder?: "asc" | "desc";
}

interface FetchNotificationsResponse {
  notifications: NotificationItem[];
  totalCount: number;
  unreadCount: number;
  hasMore: boolean;
}

interface MarkAsReadResponse {
  success: boolean;
  updatedIds: string[];
}

// Alert Settings API Service
export interface AlertSettings {
  id: number;
  user_id: number;
  all_alerts_enabled: boolean;
  payment_due_reminders_enabled: boolean;
  payment_due_first_reminder_days_before: number;
  payment_due_first_reminder_email: boolean;
  payment_due_first_reminder_sms: boolean;
  payment_due_second_reminder_days_before: number;
  payment_due_second_reminder_email: boolean;
  payment_due_second_reminder_sms: boolean;
  payment_due_third_reminder_days_before: number;
  payment_due_third_reminder_email: boolean;
  payment_due_third_reminder_sms: boolean;
  interest_period_alerts_enabled: boolean;
  interest_period_first_notice_days_before: number;
  interest_period_first_notice_email: boolean;
  interest_period_first_notice_sms: boolean;
  interest_period_second_notice_days_before: number;
  interest_period_second_notice_email: boolean;
  interest_period_second_notice_sms: boolean;
  interest_period_final_notice_days_before: number;
  interest_period_final_notice_email: boolean;
  interest_period_final_notice_sms: boolean;
  contact_email: string | null;
  contact_mobile: string | null;
  create_at: string;
  update_at: string;
}

// Billing API Interfaces
export interface PaymentMethod {
  id: number;
  card_brand: string;
  last_four: string;
  expiry_month: string;
  expiry_year: string;
  is_default: boolean;
}

export interface SubscriptionPlan {
  id: number;
  name: string;
  display_name: string;
  description: string;
  monthly_price: number;
  yearly_price: number;
  features: string[];
}

export interface Subscription {
  id: number;
  plan_name: string;
  display_name: string;
  status: string;
  billing_cycle: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  payment_method?: PaymentMethod;
  features: string[];
  create_at: string;
  isFree?: boolean;
}

export interface BillingTransaction {
  id: number;
  user_id: number;
  subscription_id: number;
  amount: number;
  currency: string;
  status: string;
  description: string;
  receipt_url: string;
  create_at: string;
  card_brand: string;
  last_four: string;
  plan_name: string;
  billing_cycle: string;
}

export interface BillingHistoryPagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasMore: boolean;
}

export const billingApi = {
  // Get all subscription plans
  getSubscriptionPlans: async (): Promise<{
    success: boolean;
    plans: SubscriptionPlan[];
    publishableKey: string;
  }> => {
    const response = await api.get("/billing/plans");
    return response.data;
  },

  // Get current subscription
  getCurrentSubscription: async (): Promise<{
    success: boolean;
    subscription: Subscription;
  }> => {
    const response = await api.get("/billing/subscription");
    return response.data;
  },

  // Get payment methods
  getPaymentMethods: async (): Promise<{
    success: boolean;
    paymentMethods: PaymentMethod[];
  }> => {
    const response = await api.get("/billing/payment-methods");
    return response.data;
  },

  // Add a payment method
  addPaymentMethod: async (
    paymentMethodId: string,
    setAsDefault = false
  ): Promise<{
    success: boolean;
    message: string;
    paymentMethod: PaymentMethod;
  }> => {
    const response = await api.post("/billing/payment-methods", {
      paymentMethodId,
      setAsDefault,
    });
    return response.data;
  },

  // Delete a payment method
  deletePaymentMethod: async (
    paymentMethodId: number
  ): Promise<{
    success: boolean;
    message: string;
  }> => {
    const response = await api.delete(
      `/billing/payment-methods/${paymentMethodId}`
    );
    return response.data;
  },

  // Set default payment method
  setDefaultPaymentMethod: async (
    paymentMethodId: number
  ): Promise<{
    success: boolean;
    message: string;
  }> => {
    const response = await api.put(
      `/billing/payment-methods/${paymentMethodId}/default`
    );
    return response.data;
  },

  // Create a subscription
  createSubscription: async (
    planName: string,
    billingCycle: string,
    paymentMethodId: number
  ): Promise<{
    success: boolean;
    message: string;
    subscription: Subscription;
  }> => {
    const response = await api.post("/billing/subscriptions", {
      planName,
      billingCycle,
      paymentMethodId,
    });
    return response.data;
  },

  // Cancel a subscription
  cancelSubscription: async (
    subscriptionId: number,
    cancelAtPeriodEnd = true
  ): Promise<{
    success: boolean;
    message: string;
  }> => {
    const response = await api.post(
      `/billing/subscriptions/${subscriptionId}/cancel`,
      {
        cancelAtPeriodEnd,
      }
    );
    return response.data;
  },

  // Resume a subscription
  resumeSubscription: async (
    subscriptionId: number
  ): Promise<{
    success: boolean;
    message: string;
  }> => {
    const response = await api.post(
      `/billing/subscriptions/${subscriptionId}/resume`
    );
    return response.data;
  },

  // Get billing history
  getBillingHistory: async (
    page = 1,
    limit = 10
  ): Promise<{
    success: boolean;
    transactions: BillingTransaction[];
    pagination: BillingHistoryPagination;
  }> => {
    const response = await api.get("/billing/history", {
      params: { page, limit },
    });
    return response.data;
  },
};

export interface UpdateAlertSettingsPayload {
  all_alerts_enabled?: boolean;
  payment_due_reminders_enabled?: boolean;
  payment_due_first_reminder_days_before?: number;
  payment_due_first_reminder_email?: boolean;
  payment_due_first_reminder_sms?: boolean;
  payment_due_second_reminder_days_before?: number;
  payment_due_second_reminder_email?: boolean;
  payment_due_second_reminder_sms?: boolean;
  payment_due_third_reminder_days_before?: number;
  payment_due_third_reminder_email?: boolean;
  payment_due_third_reminder_sms?: boolean;
  interest_period_alerts_enabled?: boolean;
  interest_period_first_notice_days_before?: number;
  interest_period_first_notice_email?: boolean;
  interest_period_first_notice_sms?: boolean;
  interest_period_second_notice_days_before?: number;
  interest_period_second_notice_email?: boolean;
  interest_period_second_notice_sms?: boolean;
  interest_period_final_notice_days_before?: number;
  interest_period_final_notice_email?: boolean;
  interest_period_final_notice_sms?: boolean;
  contact_email?: string | null;
  contact_mobile?: string | null;
}

export interface AlertHistoryItem {
  id: number;
  user_id: number;
  alert_type: string;
  title: string;
  message: string;
  entity_id: string | null;
  entity_type: string | null;
  send_method: string;
  is_sent: boolean;
  is_delivered: boolean;
  is_read: boolean;
  is_acted_upon: boolean;
  delivery_status: string | null;
  status: string;
  create_at: string;
  update_at: string;
}

export interface AlertHistoryPagination {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasMore: boolean;
}

export interface GetAlertHistoryParams {
  page?: number;
  limit?: number;
  alert_type?: string;
  status?: string;
  sort_by?: string;
  sort_order?: "asc" | "desc";
}

export interface UpdateAlertHistoryStatusPayload {
  status?: string;
  is_read?: boolean;
  is_acted_upon?: boolean;
}

export const alertSettingsApi = {
  getSettings: async (): Promise<{
    success: boolean;
    settings: AlertSettings;
  }> => {
    const response = await api.get("/alert-settings");
    return response.data;
  },

  updateSettings: async (
    payload: UpdateAlertSettingsPayload
  ): Promise<{
    success: boolean;
    settings: AlertSettings;
    message?: string;
  }> => {
    const response = await api.put("/alert-settings", payload);
    return response.data;
  },

  // New methods for alert history
  getAlertHistory: async (
    params: GetAlertHistoryParams = {}
  ): Promise<{
    success: boolean;
    alertHistory: AlertHistoryItem[];
    pagination: AlertHistoryPagination;
  }> => {
    const response = await api.get("/alert-settings/history", { params });
    return response.data;
  },

  updateAlertHistoryStatus: async (
    alertId: number | string,
    payload: UpdateAlertHistoryStatusPayload
  ): Promise<{
    success: boolean;
    alert: AlertHistoryItem;
    message?: string;
  }> => {
    const response = await api.put(
      `/alert-settings/history/${alertId}`,
      payload
    );
    return response.data;
  },
};

// Notification API service
export const notificationsApi = {
  baseUrl: API_URL,

  /**
   * Fetch notifications for the current user
   */
  async fetchNotifications(
    params: FetchNotificationsParams = {}
  ): Promise<FetchNotificationsResponse> {
    const {
      page = 1,
      limit = 10,
      unreadOnly = false,
      types = [],
      sortBy = "date",
      sortOrder = "desc",
    } = params;

    try {
      const response = await api.get(`/notifications`, {
        params: {
          page,
          limit,
          unread_only: unreadOnly,
          types: types.length > 0 ? types.join(",") : undefined,
          sort_by: sortBy,
          sort_order: sortOrder,
        },
      });

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Mark notifications as read
   * @param ids - Array of notification IDs to mark as read, or 'all' to mark all as read
   */
  async markAsRead(ids: string[] | "all"): Promise<MarkAsReadResponse> {
    try {
      const response = await api.post(`/notifications/mark-read`, {
        notification_ids: ids,
      });

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get the count of unread notifications
   */
  async getUnreadCount(): Promise<number> {
    try {
      const response = await api.get(`/notifications/unread-count`);

      return response.data.count;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Delete a notification
   */
  async deleteNotification(id: string): Promise<{ success: boolean }> {
    try {
      const response = await api.delete(`/notifications/${id}`);

      return { success: response.data.success };
    } catch (error) {
      throw error;
    }
  },
};

export interface CustomAlert {
  id: number;
  user_id: number;
  name: string;
  description: string;
  alert_type: "spending_threshold" | "balance" | "reward" | "custom";
  threshold_value: number | null;
  category: string | null;
  card_id: number | null;
  is_email_enabled: boolean;
  is_sms_enabled: boolean;
  is_active: boolean;
  create_at: string;
  update_at: string;
  // Joined fields
  card_name?: string;
  last_four?: string;
  issuer?: string;
}

export const customAlertApi = {
  // Get all custom alerts
  getUserAlerts: async (): Promise<{
    success: boolean;
    alerts: CustomAlert[];
    message?: string;
  }> => {
    const response = await api.get("/custom-alerts");
    return response.data;
  },

  // Create a new custom alert
  createAlert: async (
    alertData: Partial<CustomAlert>
  ): Promise<{
    success: boolean;
    alert?: CustomAlert;
    message?: string;
    requiresUpgrade?: boolean;
  }> => {
    const response = await api.post("/custom-alerts", alertData);
    return response.data;
  },

  // Update an existing alert
  updateAlert: async (
    alertId: number,
    alertData: Partial<CustomAlert>
  ): Promise<{
    success: boolean;
    alert?: CustomAlert;
    message?: string;
  }> => {
    const response = await api.put(`/custom-alerts/${alertId}`, alertData);
    return response.data;
  },

  // Delete an alert
  deleteAlert: async (
    alertId: number
  ): Promise<{
    success: boolean;
    message?: string;
  }> => {
    const response = await api.delete(`/custom-alerts/${alertId}`);
    return response.data;
  },
};

export default api;
